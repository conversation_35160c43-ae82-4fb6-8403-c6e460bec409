from aws_lambda_powertools.utilities.typing import LambdaContext
from aws_lambda_powertools import Logger

from ..context import Context

logger = Logger()


@logger.inject_lambda_context(log_event=True)
def handler(event: dict, _context: LambdaContext):
    """
    Lambda function to monitor SoftEther VPN server status and update metrics.
    This function connects to the SoftEther VPN server, retrieves the list of users
    and their connection status, and updates metrics accordingly.
    """
    app_context = Context()

    try:
        hubs = app_context.softether.enum_hub()
        logger.debug(f"Found {len(hubs)} hubs to monitor")
    except Exception as e:
        logger.error(f"Failed to enumerate hubs: {e}")
        return {"statusCode": 500, "error": "Failed to enumerate hubs"}

    for hub in hubs:
        hubname = hub["HubName_str"]
        if hubname in app_context.settings.ignore_hubs:
            logger.debug(f"Skipping ignored hub: {hubname}")
            continue  # explicit opt‑out

        logger.debug(f"Processing hub: {hubname}")
        
        process_hub(app_context, hubname)

    return {"statusCode": 200}


def process_hub(app_context: Context, hubname: str) -> None:
    # Get users and sessions for this hub
    all_users = app_context.softether.enum_user(hubname)
    online_sessions = app_context.softether.enum_session(hubname)

    # Set of usernames currently online
    online_usernames = {session["Username_str"] for session in online_sessions}

    logger.info(f"Hub {hubname}: {len(all_users)} users, {len(online_sessions)} online sessions")

    # For each user in the hub
    for user in all_users:
        username = user["Name_str"]

        if any(keyword in username.lower() for keyword in ("icinga", "dhcp")):
            continue  # Skip icinga and dhcp servers

        is_connected = username in online_usernames
        app_context.server_status_metric.update(username, is_connected)
