from pydantic_settings import SettingsConfigDict


class Settings():
    ignore_hubs: list[str] = []
    environment: str = "dev"

    # SoftEther settings
    softether_host: str
    softether_port: int
    softether_password: str

    # Metrics settings
    victoria_metrics_url: str
    victoria_metrics_username: str
    victoria_metrics_password: str

    model_config = SettingsConfigDict(env_file=".env", extra="ignore")
