import urllib3
import requests
from typing import TypedDict
import logging


# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

logger = logging.getLogger(__name__)


class Hub(TypedDict):
    HubName_str: str
    HubType_u32: int
    IsTrafficFilled_bool: bool
    LastCommTime_dt: str
    LastLoginTime_dt: str
    NumGroups_u32: int
    NumIpTables_u32: int
    NumLogin_u32: int
    NumMacTables_u32: int
    NumSessions_u32: int
    NumUsers_u32: int
    Online_bool: bool
    CreatedTime_dt: str


class Session(TypedDict):
    BridgeMode_bool: bool
    ClientIP_ip: str
    Client_BridgeMode_bool: bool
    Client_MonitorMode_bool: bool
    CreatedTime_dt: str
    CurrentNumTcp_u32: int
    Hostname_str: str
    Ip_ip: str
    IsDormant_bool: bool
    IsDormantEnabled_bool: bool
    LastCommDormant_dt: str
    LastCommTime_dt: str
    Layer3Mode_bool: bool
    LinkMode_bool: bool
    MaxNumTcp_u32: int
    Name_str: str
    PacketNum_u64: int
    PacketSize_u64: int
    RemoteHostname_str: str
    RemoteSession_bool: bool
    SecureNATMode_bool: bool
    UniqueId_bin: str
    Username_str: str
    VLanId_u32: int


class User(TypedDict):
    AuthType_u32: int
    DenyAccess_bool: bool
    Expires_dt: str
    GroupName_str: str
    IsExpiresFilled_bool: bool
    IsTrafficFilled_bool: bool
    LastLoginTime_dt: str
    Name_str: str
    Note_utf: str
    NumLogin_u32: int
    Realname_utf: str


class SoftEther:
    def __init__(
        self,
        host: str,
        port: int,
        hubname: str = "",
        password: str = "",
        verify: bool = False,
        timeout: int = 30,
    ) -> None:
        self.host = host
        self.port = port
        self.password = password
        self.timeout = timeout

        self.client = requests.session()
        self.client.verify = verify
        self.client.headers.update(
            {
                "Content-Type": "application/json",
                "X-VPNADMIN-HUBNAME": hubname,
                "X-VPNADMIN-PASSWORD": password,
            }
        )

    def _send_request(self, method: str, params: dict | None = None) -> dict:
        url = f"https://{self.host}:{self.port}/api"
        try:
            response = self.client.post(
                url,
                json={
                    "jsonrpc": "2.0",
                    "id": "rpc_call_id",
                    "method": method,
                    "params": {} if params is None else params,
                },
                timeout=self.timeout,
            )
            response.raise_for_status()
            return response.json()  # type: ignore[no-any-return]
        except (requests.exceptions.RequestException, requests.exceptions.Timeout) as e:
            logger.error(f"SoftEther API request failed for method {method}: {e}")
            raise

    def enum_hub(self) -> list[Hub]:
        response = self._send_request("EnumHub")
        return [Hub(**hub) for hub in response["result"]["HubList"]]

    def enum_session(self, hubname: str) -> list[Session]:
        response = self._send_request("EnumSession", params={"HubName_str": hubname})
        return [Session(**session) for session in response["result"]["SessionList"]]

    def enum_user(self, hubname: str) -> list[User]:
        response = self._send_request("EnumUser", params={"HubName_str": hubname})
        return [User(**user) for user in response["result"]["UserList"]]
