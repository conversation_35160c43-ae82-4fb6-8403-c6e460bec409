from httpx import Client


class DowntimeService:
    def __init__(self, downtime_api_url: str) -> None:
        self.api_url = downtime_api_url
        self.client = Client(base_url=downtime_api_url)

    def is_site_alerts_disabled(self, site_id: str):
        # Checks /api/v1/maintenance?site_id={site_id}&active=true
        response = self.client.get(f"/api/v1/maintenance?site_id={site_id}&active=true")
        sites = response.json()
        return len(sites) > 0
