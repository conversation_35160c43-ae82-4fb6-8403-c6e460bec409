from pydantic_settings import SettingsConfigDict


class NetsuiteDefaultFields:
    customer_id: str
    status_pending: str = "Pending"
    status_closed: str = "Closed"
    origin_platform: str = "Platform"                         # TODO: <PERSON><PERSON> should set this in a label
    category_l2_camera_offline: str = "Eyecue Camera Offline" # TODO: <PERSON><PERSON> should set this in a label
    priority_p3: str = "P3"                                   # TODO: <PERSON><PERSON> should set this in a label
    product_eyecue: str = "EyeCue"

    model_config = SettingsConfigDict(env_file=".env", extra="ignore", env_prefix="NETSUITE_")


class Settings:
    downtime_api_url: str = "https://aqn8c2wdu1.execute-api.ap-southeast-2.amazonaws.com/dev/"
    netsuite_fields: NetsuiteDefaultFields = NetsuiteDefaultFields()

    model_config = SettingsConfigDict(env_file=".env", extra="ignore")

