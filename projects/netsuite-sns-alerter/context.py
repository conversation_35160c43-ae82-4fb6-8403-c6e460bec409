from dataclasses import dataclass, field
from netsuite.client import Config

from config import Settings
from services.netsuite import Netsuite
from services.alerting import <PERSON><PERSON><PERSON><PERSON><PERSON>
from services.downtime import DowntimeService


@dataclass
class Context:
    settings: Settings = field(default_factory=lambda: Settings())
    netsuite_config: Config = field(default_factory=Config.from_env)
    netsuite: Netsuite = field(init=False)
    alert_handler: AlertHandler = field(init=False)
    downtime_service: DowntimeService = field(init=False)

    def __post_init__(self) -> None:
        self.netsuite = Netsuite(self.netsuite_config)
        self.downtime_service = DowntimeService(self.settings.downtime_api_url)
        self.alert_handler = AlertHandler(self)
