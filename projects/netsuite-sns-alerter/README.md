# Netsuite Alert Hook (SNS)

A generic alerter for Netsuite Support Tickets via SNS events.


## Creating a new Grafana Alert

### 1. Create a Notification Template

1. Go to `Alerting` > `Contact points`
2. Select `Notification Templates` tab
3. Click `Add notification template group`.
4. Enter a Template Group Name, e.g. "Generic Template for Netsuite SNS Alerter"
    ```go
    {{ define "netsuite.sns.json" }}
    {
    "alerts": [
        {{- range $aIndex, $alert := .Alerts }}
            {{- if $aIndex }},{{ end }}
            {
            "status": "{{ $alert.Status }}",
            "title": {{ printf "%q" $alert.Annotations.summary }},
            "message": {{ printf "%q" $alert.Annotations.description }},
            "site_id": {{ printf "%q" $alert.Labels.site_id }},
            "fingerprint": {{ printf "%q" $alert.Fingerprint }},
            "startsAt": {{ printf "%q" $alert.StartsAt }}
            "labels": {
                {{- $first := true }}
                {{- range $k, $v := $alert.Labels }}
                {{- if not $first }},{{ end }}
                {{ printf "%q" $k }}: {{ printf "%q" $v }}
                {{- $first = false }}
                {{- end }}
            }
            }
        {{- end }}
    ]
    }
    {{ end }}
    ```

