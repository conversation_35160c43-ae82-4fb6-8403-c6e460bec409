import os
from pynamodb.models import Model
from pynamodb.attributes import UnicodeAttribute, UTCDateTimeAttribute


class MaintenancePeriod(Model):

    class Meta: # type: ignore
        table_name = os.environ.get("MAINTENANCE_PERIODS_TABLE", "maintenance-periods-dev")
        region = "ap-southeast-2"

    id = UnicodeAttribute(hash_key=True)
    start = UTCDateTimeAttribute()
    end = UTCDateTimeAttribute(null=True)
    site_id_regex = UnicodeAttribute(null=True)
