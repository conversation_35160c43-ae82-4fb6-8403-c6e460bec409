import pytest
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, patch, MagicMock
from uuid import uuid4

from api.v1.maintenance.controller import MaintenanceController
from api.v1.maintenance.schemas import (
    CreateMaintenancePeriodRequest,
    GetMaintenancePeriodResponse,
)
from core.models import MaintenancePeriod
from context import Context


class TestMaintenanceController:
    @pytest.fixture
    def mock_context(self):
        """Create a mock context for testing."""
        context = Mock(spec=Context)
        return context

    @pytest.fixture
    def controller(self, mock_context):
        """Create a MaintenanceController instance for testing."""
        return MaintenanceController(mock_context)

    @pytest.fixture
    def now(self):
        """Current time for testing."""
        return datetime.now(timezone.utc)

    def test_get_active_maintenance_periods_with_null_end_date(self, controller, now):
        """Test that maintenance periods with null end dates are returned when active=True."""
        # Mock maintenance periods - only the active ones should be returned by scan
        mock_period_with_end = Mock()
        mock_period_with_end.attribute_values = {
            'id': str(uuid4()),
            'start': now - timedelta(hours=1),
            'end': now + timedelta(hours=1),
            'site_id_regex': 'fm-mcd-*'
        }

        mock_period_without_end = Mock()
        mock_period_without_end.attribute_values = {
            'id': str(uuid4()),
            'start': now - timedelta(hours=1),
            'end': None,
            'site_id_regex': 'fm-mcd-*'
        }

        # Mock the scan method to return only the active periods
        # (simulating that DynamoDB filtering worked correctly)
        with patch.object(MaintenancePeriod, 'scan') as mock_scan:
            mock_scan.return_value = [
                mock_period_with_end,
                mock_period_without_end
            ]

            # Call the method with active=True
            result = controller.get_maintenance_periods(site_id=None, active=True)

            # Verify that the scan was called with a filter condition
            mock_scan.assert_called_once()
            call_args = mock_scan.call_args
            assert 'filter_condition' in call_args.kwargs
            assert call_args.kwargs['filter_condition'] is not None

            # The result should include both active periods (with and without end date)
            assert len(result) == 2

            # Verify the returned periods are the correct ones
            returned_ids = {period.id for period in result}
            expected_ids = {
                mock_period_with_end.attribute_values['id'],
                mock_period_without_end.attribute_values['id']
            }
            assert returned_ids == expected_ids

    def test_get_maintenance_periods_without_active_filter(self, controller):
        """Test that all maintenance periods are returned when active=None."""
        mock_period1 = Mock()
        mock_period1.attribute_values = {
            'id': str(uuid4()),
            'start': datetime.now(timezone.utc),
            'end': None,
            'site_id_regex': 'fm-mcd-*'
        }

        mock_period2 = Mock()
        mock_period2.attribute_values = {
            'id': str(uuid4()),
            'start': datetime.now(timezone.utc),
            'end': datetime.now(timezone.utc) + timedelta(hours=1),
            'site_id_regex': 'fm-mcd-*'
        }

        with patch.object(MaintenancePeriod, 'scan') as mock_scan:
            mock_scan.return_value = [mock_period1, mock_period2]

            result = controller.get_maintenance_periods(site_id=None, active=None)

            # Should call scan without filter condition
            mock_scan.assert_called_once_with(filter_condition=None)
            
            # Should return all periods
            assert len(result) == 2

    def test_get_maintenance_periods_with_site_id_filter(self, controller):
        """Test filtering by site_id using regex matching."""
        mock_period1 = Mock()
        mock_period1.attribute_values = {
            'id': str(uuid4()),
            'start': datetime.now(timezone.utc),
            'end': None,
            'site_id_regex': 'fm-mcd-.*'
        }
        mock_period1.site_id_regex = 'fm-mcd-.*'

        mock_period2 = Mock()
        mock_period2.attribute_values = {
            'id': str(uuid4()),
            'start': datetime.now(timezone.utc),
            'end': None,
            'site_id_regex': 'fm-kfc-.*'
        }
        mock_period2.site_id_regex = 'fm-kfc-.*'

        with patch.object(MaintenancePeriod, 'scan') as mock_scan:
            mock_scan.return_value = [mock_period1, mock_period2]

            result = controller.get_maintenance_periods(site_id='fm-mcd-123', active=None)

            # Should return only the matching period
            assert len(result) == 1
            assert result[0].site_id_regex == 'fm-mcd-.*'

    def test_create_maintenance_period_with_null_end(self, controller):
        """Test creating a maintenance period with null end date."""
        request = CreateMaintenancePeriodRequest(
            start=datetime.now(timezone.utc),
            end=None,
            site_id_regex='fm-mcd-.*'
        )

        with patch.object(MaintenancePeriod, 'save') as mock_save:
            result = controller.create_maintenance_period(request)

            # Should return a response with an ID
            assert result.id is not None

            # Should have called save on the period
            mock_save.assert_called_once()

    def test_active_filter_condition_structure(self, controller, now):
        """Test that the active filter creates the correct PynamoDB condition structure."""
        with patch.object(MaintenancePeriod, 'scan') as mock_scan:
            mock_scan.return_value = []

            # Call the method with active=True
            controller.get_maintenance_periods(site_id=None, active=True)

            # Verify that scan was called with a condition
            mock_scan.assert_called_once()
            call_args = mock_scan.call_args
            filter_condition = call_args.kwargs['filter_condition']

            # The condition should not be None when active=True
            assert filter_condition is not None

            # The condition should be a compound condition (AND operation)
            # We can't easily test the exact structure without diving into PynamoDB internals,
            # but we can verify it's not None and was constructed
            assert hasattr(filter_condition, 'serialize')

    def test_no_filter_when_active_false(self, controller):
        """Test that no filter is applied when active=False or None."""
        with patch.object(MaintenancePeriod, 'scan') as mock_scan:
            mock_scan.return_value = []

            # Test with active=False
            controller.get_maintenance_periods(site_id=None, active=False)
            mock_scan.assert_called_with(filter_condition=None)

            # Reset mock
            mock_scan.reset_mock()

            # Test with active=None
            controller.get_maintenance_periods(site_id=None, active=None)
            mock_scan.assert_called_with(filter_condition=None)


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
