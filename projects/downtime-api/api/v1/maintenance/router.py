from typing import TYPE_CHECKING

from api.v1.maintenance.schemas import (
    GetMaintenancePeriodResponse,
    CreateMaintenancePeriodRequest,
    CreateMaintenancePeriodResponse,
    UpdateMaintenancePeriodRequest,
    UpdateMaintenancePeriodResponse,
    DeleteMaintenancePeriodResponse,
)

if TYPE_CHECKING:
    from context import Context

def setup_routes(prefix: str, context: "Context"):
    @context.app.get(prefix)
    def get_maintenance_periods(site_id: str | None = None, active: bool | None = None) -> list[GetMaintenancePeriodResponse]:
        return context.maintenance_controller.get_maintenance_periods(site_id, active)

    @context.app.get(f"{prefix}/<id>")
    def get_maintenance_period_by_id(id: str) -> GetMaintenancePeriodResponse:
        return context.maintenance_controller.get_maintenance_period_by_id(id)

    @context.app.post(prefix)
    def create_maintenance_period(options: CreateMaintenancePeriodRequest) -> CreateMaintenancePeriodResponse:
        return context.maintenance_controller.create_maintenance_period(options)
    
    @context.app.put(prefix)
    def update_maintenance_period(options: UpdateMaintenancePeriodRequest) -> UpdateMaintenancePeriodResponse:
        return context.maintenance_controller.update_maintenance_period(options)
    
    @context.app.delete(f"{prefix}/<id>")
    def delete_maintenance_period(id: str) -> DeleteMaintenancePeriodResponse:
        return context.maintenance_controller.delete_maintenance_period(id)
