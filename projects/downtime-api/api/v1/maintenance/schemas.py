from datetime import datetime
from pydantic import BaseModel, Field


# --- Create Maintenance Period --- #

class CreateMaintenancePeriodRequest(BaseModel):
    start: datetime = Field(description="Start of the maintenance period", default_factory=datetime.now)
    end: datetime | None = Field(None, description="End of the maintenance period")
    site_id_regex: str = Field(description="Regex to match site IDs to apply the maintenance period to")


class CreateMaintenancePeriodResponse(BaseModel):
    id: str = Field(description="ID of the created maintenance period")


# --- Get Maintenance Period --- #

class GetMaintenancePeriodResponse(BaseModel):
    id: str = Field(description="ID of the maintenance period")
    start: datetime = Field(description="Start of the maintenance period")
    end: datetime | None = Field(None, description="End of the maintenance period")
    site_id_regex: str = Field(description="Regex to match site IDs to apply the maintenance period to")


# --- Update Maintenance Period --- #

class UpdateMaintenancePeriodRequest(BaseModel):
    id: str = Field(description="ID of the maintenance period to update")
    start: datetime | None = Field(None, description="Start of the maintenance period")
    end: datetime | None = Field(None, description="End of the maintenance period")
    site_id_regex: str = Field(description="Regex to match site IDs to apply the maintenance period to")


class UpdateMaintenancePeriodResponse(BaseModel):
    id: str = Field(description="ID of the updated maintenance period")


# --- Delete Maintenance Period --- #

class DeleteMaintenancePeriodResponse(BaseModel):
    id: str = Field(description="ID of the deleted maintenance period")
