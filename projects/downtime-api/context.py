from dataclasses import dataclass, field
from aws_lambda_powertools.event_handler import APIGatewayRestResolver

from api.v1.maintenance.controller import MaintenanceController


@dataclass
class Context:
    app: APIGatewayRestResolver = field(
        default_factory=lambda: APIGatewayRestResolver(enable_validation=True))
    maintenance_controller: MaintenanceController = field(init=False)
    

    def __post_init__(self) -> None:
        self.maintenance_controller = MaintenanceController(self)
