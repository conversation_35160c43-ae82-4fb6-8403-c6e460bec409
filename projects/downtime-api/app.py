from aws_lambda_powertools.utilities.typing.lambda_context import LambdaContext

from context import Context
from api.v1.maintenance.router import setup_routes as setup_maintenance_routes

app_context = Context()

# Setup routes
setup_maintenance_routes(prefix="/api/v1/maintenance", context=app_context)

def lambda_handler(event: dict, context: LambdaContext) -> dict:
    return app_context.app.resolve(event, context)
