AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: >
  Downtime API - API for managing maintenance periods and downtime schedules

Globals:
  Function:
    Runtime: python3.13
    Timeout: 30
    MemorySize: 256
    Tracing: Active
    Environment:
      Variables:
        ENVIRONMENT: !Ref Environment
        POWERTOOLS_SERVICE_NAME: !Ref AWS::StackName
        POWERTOOLS_LOG_LEVEL: !Ref LogLevel
    Tags:
      Environment: !Ref Environment
      Team: Platform

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues:
      - dev
      - prod
    Description: Deployment environment

  LogLevel:
    Type: String
    Default: INFO
    AllowedValues:
      - DEBUG
      - INFO
      - WARNING
      - ERROR
      - CRITICAL
    Description: Log level for the Lambda function

Resources:
  # API Gateway
  DowntimeApi:
    Type: AWS::Serverless::Api
    Properties:
      StageName: !Ref Environment
      TracingEnabled: true
      Cors:
        AllowOrigin: "'*'"
        AllowHeaders: "'Content-Type,Authorization,X-Amz-Date'"
        AllowMethods: "'GET,POST,PUT,DELETE,OPTIONS'"
        MaxAge: "'300'"

  # DynamoDB Table for Maintenance Periods
  MaintenancePeriodsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "maintenance-periods-${Environment}"
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Squad
          Value: platform

  # Lambda Function
  DowntimeApiFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "downtime-api-${Environment}"
      Handler: app.lambda_handler
      CodeUri: .
      Description: "API for managing maintenance periods and downtime schedules"
      Environment:
        Variables:
          ENVIRONMENT: !Ref Environment
          MAINTENANCE_PERIODS_TABLE: !Ref MaintenancePeriodsTable
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref MaintenancePeriodsTable
      Events:
        GetMaintenancePeriods:
          Type: Api
          Properties:
            RestApiId: !Ref DowntimeApi
            Path: /api/v1/maintenance
            Method: GET
        CreateMaintenancePeriod:
          Type: Api
          Properties:
            RestApiId: !Ref DowntimeApi
            Path: /api/v1/maintenance
            Method: POST
        UpdateMaintenancePeriod:
          Type: Api
          Properties:
            RestApiId: !Ref DowntimeApi
            Path: /api/v1/maintenance
            Method: PUT
        DeleteMaintenancePeriod:
          Type: Api
          Properties:
            RestApiId: !Ref DowntimeApi
            Path: /api/v1/maintenance
            Method: DELETE
        GetMaintenancePeriodById:
          Type: Api
          Properties:
            RestApiId: !Ref DowntimeApi
            Path: /api/v1/maintenance/{id}
            Method: GET

Outputs:
  DowntimeApiUrl:
    Description: "API Gateway endpoint URL for Downtime API"
    Value: !Sub "https://${DowntimeApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}/"
    Export:
      Name: !Sub "${AWS::StackName}-DowntimeApiUrl"

  MaintenancePeriodsTableName:
    Description: "DynamoDB table name for maintenance periods"
    Value: !Ref MaintenancePeriodsTable
    Export:
      Name: !Sub "${AWS::StackName}-MaintenancePeriodsTable"